apiVersion: v1
kind: ConfigMap
metadata:
  name: flink-cdc-config
  namespace: default
data:
  # 目标数据库配置
  TARGET_HOSTNAME: "your-tdsdata-host"
  TARGET_PORT: "54321"
  TARGET_DATABASE: "tdsdata"
  TARGET_USERNAME: "system"
  # TARGET_PASSWORD 应该通过 Secret 管理
  
  # 源数据库配置
  CYXDMS_HOSTNAME: "cyxdms-retail-host"
  CYXDMS_PORT: "3306"
  CYXDMS_USERNAME: "flink_user"
  
  EPSAL_HOSTNAME: "ep-sal-host"
  EPSAL_PORT: "3306"
  EPSAL_USERNAME: "flink_user"
  
  EPSCSYS_HOSTNAME: "ep-sc-sys-host"
  EPSCSYS_PORT: "3306"
  EPSCSYS_USERNAME: "flink_user"
  
  EPSCVC_HOSTNAME: "ep-sc-vc-host"
  EPSCVC_PORT: "3306"
  EPSCVC_USERNAME: "flink_user"

---
apiVersion: v1
kind: Secret
metadata:
  name: flink-cdc-secrets
  namespace: default
type: Opaque
data:
  # 注意：这些值需要进行 base64 编码
  # echo -n "King@123" | base64
  TARGET_PASSWORD: S2luZ0AxMjM=
  # echo -n "flink_password" | base64
  CYXDMS_PASSWORD: ZmxpbmtfcGFzc3dvcmQ=
  EPSAL_PASSWORD: ZmxpbmtfcGFzc3dvcmQ=
  EPSCSYS_PASSWORD: ZmxpbmtfcGFzc3dvcmQ=
  EPSCVC_PASSWORD: ZmxpbmtfcGFzc3dvcmQ=

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: flink-cdc-multi-source-sync
  namespace: default
  labels:
    app: flink-cdc-multi-source-sync
spec:
  replicas: 1
  selector:
    matchLabels:
      app: flink-cdc-multi-source-sync
  template:
    metadata:
      labels:
        app: flink-cdc-multi-source-sync
    spec:
      containers:
      - name: flink-cdc-app
        image: your-registry/flink-cdc-multi-source-sync:latest
        imagePullPolicy: Always
        
        # 环境变量配置
        envFrom:
        - configMapRef:
            name: flink-cdc-config
        - secretRef:
            name: flink-cdc-secrets
        
        # 资源限制
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        
        # 健康检查
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "ps aux | grep java | grep -v grep"
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "ps aux | grep java | grep -v grep"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        # 卷挂载
        volumeMounts:
        - name: logs
          mountPath: /opt/flink-cdc-app/logs
        
        # 启动命令
        command: ["/opt/flink-cdc-app/docker-entrypoint.sh"]
        args: ["run"]
      
      # 卷定义
      volumes:
      - name: logs
        emptyDir: {}
      
      # 重启策略
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: flink-cdc-multi-source-sync-service
  namespace: default
  labels:
    app: flink-cdc-multi-source-sync
spec:
  selector:
    app: flink-cdc-multi-source-sync
  ports:
  - name: http
    port: 8081
    targetPort: 8081
    protocol: TCP
  type: ClusterIP

---
# 可选：如果需要外部访问，可以创建 Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: flink-cdc-multi-source-sync-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: flink-cdc.your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: flink-cdc-multi-source-sync-service
            port:
              number: 8081
