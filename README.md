# Flink CDC 多源数据同步项目

本项目使用 Apache Flink CDC 实现从多个 MySQL 和人大金仓数据库向目标人大金仓数据库的实时数据同步。支持全量+增量同步模式，适用于 Kubernetes 环境部署。

## 项目特性

- **多源支持**: 支持 MySQL 和人大金仓数据库作为数据源
- **实时同步**: 基于 Flink CDC 的实时数据变更捕获和同步
- **全量+增量**: 支持初始全量数据同步和后续增量变更同步
- **容器化部署**: 支持 Docker 和 Kubernetes 部署
- **配置化管理**: 通过配置类管理数据库连接和表结构
- **高可用**: 支持 Flink 检查点和故障恢复

## 系统架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ cyxdms_retail│    │   ep_sal    │    │ ep_sc_sys   │    │  ep_sc_vc   │
│   (MySQL)   │    │  (MySQL)    │    │  (MySQL)    │    │  (MySQL)    │
└──────┬──────┘    └──────┬──────┘    └──────┬──────┘    └──────┬──────┘
       │                  │                  │                  │
       └──────────────────┼──────────────────┼──────────────────┘
                          │                  │
                    ┌─────▼──────────────────▼─────┐
                    │      Flink CDC Job           │
                    │   (Kubernetes Pod)           │
                    └─────────────┬─────────────────┘
                                  │
                            ┌─────▼─────┐
                            │  tdsdata  │
                            │(Kingbase) │
                            └───────────┘
```

## 架构简化说明

### 简化后的优势

1. **无需定义表结构**:
   - 使用 DataStream API 替代 Table API
   - CDC 自动捕获表结构变化，无需手动定义字段
   - 移除了复杂的 `TableSchemaConfig` 类

2. **直接数据流处理**:
   - 基于 Debezium 的 JSON 格式处理 CDC 事件
   - 自动解析 INSERT、UPDATE、DELETE 操作
   - 表名和字段完全一致，直接映射到目标数据库

3. **简化的依赖管理**:
   - 移除了不必要的 Table API 依赖
   - 只保留核心的 DataStream API 和 CDC 连接器
   - 减少了依赖冲突的可能性

## 项目结构

```
flink_cdc_demo/
├── src/main/java/
│   ├── SimplifiedCDCJob.java       # 简化的 Flink CDC 作业类 (主要)
│   ├── DatabaseOperator.java       # 数据库操作工具类
│   ├── DatabaseConfig.java         # 数据库连接配置类
│   └── MultiSourceCDCJob.java      # 原始的复杂版本 (备用)
├── lib/                            # 本地依赖库
│   └── flink-sql-connector-kingbase-cdc-2.3-SNAPSHOT.jar
├── pom.xml                         # Maven 项目配置 (已简化)
├── Dockerfile                      # Docker 镜像构建文件
├── docker-entrypoint.sh           # Docker 启动脚本
├── k8s-deployment.yaml            # Kubernetes 部署配置
├── build-and-deploy.sh            # 构建和部署脚本
└── README.md                       # 项目说明文档
```

## 快速开始

### 1. 环境准备

- Java 17+
- Maven 3.6+
- Docker
- Kubernetes 集群
- 访问源数据库和目标数据库的网络连接

### 2. 配置数据库连接

编辑 `src/main/java/DatabaseConfig.java` 文件，更新数据库连接信息：

```java
// 目标数据库配置
public static final DatabaseInfo TARGET_DATABASE = new DatabaseInfo(
    "your-kingbase-host",  // 替换为实际的主机名
    54321,
    "tdsdata",
    "system",
    "your-password",       // 替换为实际的密码
    "kingbase"
);
```

### 3. 配置同步表

编辑 `src/main/java/DatabaseConfig.java` 文件，配置需要同步的表：

```java
new TableConfig(CYXDMS_RETAIL, new String[]{
    "your_table_1",
    "your_table_2",
    "your_table_3"
})
```

**注意**: 由于使用了简化架构，无需定义具体的表结构，CDC会自动处理表结构变化。

### 4. 构建和部署

使用提供的脚本进行构建和部署：

```bash
# 赋予执行权限
chmod +x build-and-deploy.sh

# 完整的构建和部署流程
./build-and-deploy.sh all

# 或者分步执行
./build-and-deploy.sh build   # 构建项目和镜像
./build-and-deploy.sh push    # 推送镜像
./build-and-deploy.sh deploy  # 部署到 K8s
```

### 5. 检查部署状态

```bash
# 检查部署状态
./build-and-deploy.sh status

# 查看 Pod 日志
kubectl logs -l app=flink-cdc-multi-source-sync -f
```

## 配置说明

### 环境变量配置

可以通过环境变量或系统属性配置数据库连接：

```bash
# 目标数据库
TARGET_HOSTNAME=your-kingbase-host
TARGET_PORT=54321
TARGET_DATABASE=tdsdata
TARGET_USERNAME=system
TARGET_PASSWORD=your-password

# 源数据库
CYXDMS_HOSTNAME=your-mysql-host
CYXDMS_PORT=3306
CYXDMS_USERNAME=flink_user
CYXDMS_PASSWORD=your-password
```

### Kubernetes 配置

在 `k8s-deployment.yaml` 中配置：

1. **ConfigMap**: 存储非敏感配置信息
2. **Secret**: 存储数据库密码等敏感信息
3. **Deployment**: 应用程序部署配置
4. **Service**: 服务暴露配置

## 监控和故障排除

### 查看应用日志

```bash
# 查看实时日志
kubectl logs -l app=flink-cdc-multi-source-sync -f

# 查看特定 Pod 日志
kubectl logs <pod-name> -f
```

### 常见问题

1. **数据库连接失败**:
   - 检查网络连接
   - 验证数据库凭据
   - 确认防火墙设置

2. **表结构不匹配**:
   - 检查源表和目标表结构是否一致
   - 更新 `TableSchemaConfig.java` 中的表定义

3. **内存不足**:
   - 调整 Kubernetes 资源限制
   - 修改 JVM 堆内存设置

## 性能优化

1. **并行度调整**: 根据数据量和资源情况调整 Flink 并行度
2. **检查点配置**: 优化检查点间隔和存储
3. **批量写入**: 调整 JDBC Sink 的批量写入参数

## 安全考虑

1. **密码管理**: 使用 Kubernetes Secret 管理敏感信息
2. **网络安全**: 配置网络策略限制访问
3. **权限控制**: 使用最小权限原则配置数据库用户

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。
