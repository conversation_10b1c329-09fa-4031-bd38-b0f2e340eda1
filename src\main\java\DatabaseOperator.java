import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSONObject;
import java.sql.*;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据库操作工具类
 * 用于执行目标数据库的增删改操作
 */
@Slf4j
public class DatabaseOperator {
    
    private static final Map<String, Connection> connectionPool = new ConcurrentHashMap<>();
    private static final DatabaseConfig.DatabaseInfo targetDb = DatabaseConfig.TARGET_DATABASE;
    
    /**
     * 获取数据库连接
     */
    private static Connection getConnection() throws SQLException {
        String key = "target_connection";
        Connection conn = connectionPool.get(key);
        
        if (conn == null || conn.isClosed()) {
            try {
                Class.forName(targetDb.getDriverClass());
                conn = DriverManager.getConnection(
                    targetDb.getJdbcUrl(),
                    targetDb.getUsername(),
                    targetDb.getPassword()
                );
                conn.setAutoCommit(true); // 自动提交
                connectionPool.put(key, conn);
                log.info("成功创建数据库连接: {}", targetDb.getJdbcUrl());
            } catch (ClassNotFoundException e) {
                throw new SQLException("数据库驱动未找到: " + targetDb.getDriverClass(), e);
            }
        }
        
        return conn;
    }
    
    /**
     * 执行INSERT操作
     */
    public static void executeInsert(String tableName, JSONObject data) {
        if (data == null || data.isEmpty()) {
            log.warn("INSERT数据为空，跳过表: {}", tableName);
            return;
        }
        
        try (Connection conn = getConnection()) {
            // 构建INSERT SQL
            StringBuilder columns = new StringBuilder();
            StringBuilder values = new StringBuilder();
            
            for (String key : data.keySet()) {
                if (columns.length() > 0) {
                    columns.append(", ");
                    values.append(", ");
                }
                columns.append(key);
                values.append("?");
            }
            
            String sql = String.format("INSERT INTO %s (%s) VALUES (%s)", 
                                     tableName, columns.toString(), values.toString());
            
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                int paramIndex = 1;
                for (String key : data.keySet()) {
                    Object value = data.get(key);
                    stmt.setObject(paramIndex++, value);
                }
                
                int rowsAffected = stmt.executeUpdate();
                log.info("INSERT成功: 表={}, 影响行数={}", tableName, rowsAffected);
                
            }
        } catch (SQLException e) {
            log.error("INSERT失败: 表={}, 数据={}", tableName, data, e);
            throw new RuntimeException("INSERT操作失败", e);
        }
    }
    
    /**
     * 执行UPDATE操作
     */
    public static void executeUpdate(String tableName, JSONObject before, JSONObject after) {
        if (after == null || after.isEmpty()) {
            log.warn("UPDATE数据为空，跳过表: {}", tableName);
            return;
        }
        
        try (Connection conn = getConnection()) {
            // 构建UPDATE SQL
            StringBuilder setClause = new StringBuilder();
            StringBuilder whereClause = new StringBuilder();
            
            // 构建SET子句
            for (String key : after.keySet()) {
                if (setClause.length() > 0) {
                    setClause.append(", ");
                }
                setClause.append(key).append(" = ?");
            }
            
            // 构建WHERE子句（使用before数据作为条件）
            if (before != null && !before.isEmpty()) {
                for (String key : before.keySet()) {
                    if (whereClause.length() > 0) {
                        whereClause.append(" AND ");
                    }
                    whereClause.append(key).append(" = ?");
                }
            } else {
                // 如果没有before数据，尝试使用主键
                Object id = after.get("id");
                if (id != null) {
                    whereClause.append("id = ?");
                } else {
                    log.error("UPDATE操作缺少WHERE条件: 表={}", tableName);
                    return;
                }
            }
            
            String sql = String.format("UPDATE %s SET %s WHERE %s", 
                                     tableName, setClause.toString(), whereClause.toString());
            
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                int paramIndex = 1;
                
                // 设置SET参数
                for (String key : after.keySet()) {
                    Object value = after.get(key);
                    stmt.setObject(paramIndex++, value);
                }
                
                // 设置WHERE参数
                if (before != null && !before.isEmpty()) {
                    for (String key : before.keySet()) {
                        Object value = before.get(key);
                        stmt.setObject(paramIndex++, value);
                    }
                } else {
                    // 使用主键作为条件
                    Object id = after.get("id");
                    stmt.setObject(paramIndex++, id);
                }
                
                int rowsAffected = stmt.executeUpdate();
                log.info("UPDATE成功: 表={}, 影响行数={}", tableName, rowsAffected);
                
            }
        } catch (SQLException e) {
            log.error("UPDATE失败: 表={}, before={}, after={}", tableName, before, after, e);
            throw new RuntimeException("UPDATE操作失败", e);
        }
    }
    
    /**
     * 执行DELETE操作
     */
    public static void executeDelete(String tableName, JSONObject data) {
        if (data == null || data.isEmpty()) {
            log.warn("DELETE数据为空，跳过表: {}", tableName);
            return;
        }
        
        try (Connection conn = getConnection()) {
            // 构建DELETE SQL
            StringBuilder whereClause = new StringBuilder();
            
            for (String key : data.keySet()) {
                if (whereClause.length() > 0) {
                    whereClause.append(" AND ");
                }
                whereClause.append(key).append(" = ?");
            }
            
            String sql = String.format("DELETE FROM %s WHERE %s", tableName, whereClause.toString());
            
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                int paramIndex = 1;
                for (String key : data.keySet()) {
                    Object value = data.get(key);
                    stmt.setObject(paramIndex++, value);
                }
                
                int rowsAffected = stmt.executeUpdate();
                log.info("DELETE成功: 表={}, 影响行数={}", tableName, rowsAffected);
                
            }
        } catch (SQLException e) {
            log.error("DELETE失败: 表={}, 数据={}", tableName, data, e);
            throw new RuntimeException("DELETE操作失败", e);
        }
    }
    
    /**
     * 测试数据库连接
     */
    public static boolean testConnection() {
        try (Connection conn = getConnection()) {
            return conn.isValid(5); // 5秒超时
        } catch (SQLException e) {
            log.error("数据库连接测试失败", e);
            return false;
        }
    }
    
    /**
     * 关闭所有连接
     */
    public static void closeAllConnections() {
        for (Connection conn : connectionPool.values()) {
            try {
                if (conn != null && !conn.isClosed()) {
                    conn.close();
                }
            } catch (SQLException e) {
                log.error("关闭数据库连接失败", e);
            }
        }
        connectionPool.clear();
        log.info("所有数据库连接已关闭");
    }
}
