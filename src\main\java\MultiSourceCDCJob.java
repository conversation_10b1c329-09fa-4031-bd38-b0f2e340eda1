//import lombok.extern.slf4j.Slf4j;
//import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
//import org.apache.flink.streaming.api.datastream.DataStream;
//import org.apache.flink.api.common.eventtime.WatermarkStrategy;
//import com.ververica.cdc.connectors.mysql.source.MySqlSource;
//import com.ververica.cdc.connectors.mysql.table.StartupOptions;
//import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
//import java.util.Properties;
//
///**
// * 用于从多个 MySQL/Kingbase 源数据库同步数据到目标 Kingbase 数据库的 Flink CDC 作业。
// * 使用 DataStream API，表名和字段完全一致，自动处理表结构。
// */
//@Slf4j
//public class MultiSourceCDCJob {
//
//    public static void main(String[] args) throws Exception {
//        // 1. 初始化执行环境
//        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
//        env.enableCheckpointing(10_000); // 每10秒做一次Checkpoint
//        env.setParallelism(2); // 设置并行度
//        env.disableOperatorChaining(); // 禁用算子链
//
//        // 2. 为每个源数据库创建并执行同步作业
//        for (DatabaseConfig.TableConfig tableConfig : DatabaseConfig.TABLE_CONFIGS) {
//            createCDCDataStream(env, tableConfig, DatabaseConfig.TARGET_DATABASE);
//        }
//
//        // 3. 启动所有任务
//        log.info("所有同步任务已定义，开始执行...");
//        env.execute("Multi-Source CDC Sync Job to Kingbase");
//    }
//
//    /**
//     * 为一个源数据库中的多个表创建CDC源表，并将数据插入到目标Kingbase数据库的同名表中。
//     *
//     * @param tableEnv Flink TableEnvironment
//     * @param tableConfig 表配置信息
//     * @param targetDatabase 目标数据库信息
//     */
//    private static void createAndStartCDCJob(StreamTableEnvironment tableEnv,
//                                             DatabaseConfig.TableConfig tableConfig,
//                                             DatabaseConfig.DatabaseInfo targetDatabase) {
//
//        DatabaseConfig.DatabaseInfo sourceDatabase = tableConfig.getSourceDatabase();
//        String[] tableNames = tableConfig.getTableNames();
//
//        for (String tableName : tableNames) {
//            try {
//                // 唯一的表标识符，用于Flink内部
//                String uniqueTableName = sourceDatabase.getDatabase() + "_" + tableName;
//
//                // 1. 创建 CDC 源表 (Source Table)
//                String tableSchema = TableSchemaConfig.getTableSchema(tableName);
//                String connector = sourceDatabase.getDatabaseType().equals("mysql") ? "mysql-cdc" : "kingbase-cdc";
//                String createSourceSql = String.format(
//                        "CREATE TABLE %s (" +
//                                "   %s" +
//                                ") WITH (" +
//                                "   'connector' = '%s'," +
//                                "   'hostname' = '%s'," +
//                                "   'port' = '%d'," +
//                                "   'username' = '%s'," +
//                                "   'password' = '%s'," +
//                                "   'database-name' = '%s'," +
//                                "   'table-name' = '%s'," +
//                                "   'scan.startup.mode' = 'initial'," +
//                                "   'scan.incremental.snapshot.enabled' = 'true'" +
//                                ")",
//                        uniqueTableName,
//                        tableSchema,
//                        connector,
//                        sourceDatabase.getHostname(),
//                        sourceDatabase.getPort(),
//                        sourceDatabase.getUsername(),
//                        sourceDatabase.getPassword(),
//                        sourceDatabase.getDatabase(),
//                        tableName
//                );
//                tableEnv.executeSql(createSourceSql);
//                log.info("成功创建源表: {}", uniqueTableName);
//
//                // 2. 创建目标表 (Sink Table)
//                // 注意：目标表必须在目标数据库中预先创建好，且结构与源表完全相同。
//                String createSinkSql = String.format(
//                        "CREATE TABLE %s (" +
//                                "   %s" +
//                                ") WITH (" +
//                                "   'connector' = 'jdbc'," +
//                                "   'url' = '%s'," +
//                                "   'driver' = '%s'," +
//                                "   'table-name' = '%s'," +
//                                "   'username' = '%s'," +
//                                "   'password' = '%s'," +
//                                "   'sink.buffer-flush.max-rows' = '1000'," +
//                                "   'sink.buffer-flush.interval' = '2s'," +
//                                "   'sink.max-retries' = '3'," +
//                                "   'sink.semantic' = 'exactly-once'" +
//                                ")",
//                        uniqueTableName + "_sink",
//                        tableSchema,
//                        targetDatabase.getJdbcUrl(),
//                        targetDatabase.getDriverClass(),
//                        tableName,
//                        targetDatabase.getUsername(),
//                        targetDatabase.getPassword()
//                );
//                tableEnv.executeSql(createSinkSql);
//                log.info("成功创建目标表: {}", uniqueTableName + "_sink");
//
//                // 3. 执行数据插入 (INSERT INTO)
//                // 将源表的数据插入到目标表
//                String insertSql = String.format(
//                        "INSERT INTO %s SELECT * FROM %s",
//                        uniqueTableName + "_sink",
//                        uniqueTableName
//                );
//                tableEnv.executeSql(insertSql);
//                log.info("已启动数据同步任务: {} -> {}", uniqueTableName, uniqueTableName + "_sink");
//
//            } catch (Exception e) {
//                log.error("处理表 {}.{} 时发生错误: {}", sourceDatabase.getDatabase(), tableName, e.getMessage(), e);
//                // 可以选择继续处理其他表，或者抛出异常终止
//                throw new RuntimeException(e);
//            }
//        }
//    }
//}