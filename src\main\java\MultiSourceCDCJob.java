import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableResult;

/**
 * 用于从多个 MySQL 源数据库同步数据到目标 Kingbase 数据库的 Flink CDC 作业。
 * 本作业使用 Flink SQL，确保源表和目标表的表名、字段名完全一致。
 */
@Slf4j
public class MultiSourceCDCJob {

    public static void main(String[] args) throws Exception {
        // 1. 初始化执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.enableCheckpointing(10_000); // 每10秒做一次Checkpoint
        env.setParallelism(1); // 为简单起见，设置并行度为1

        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env);

        // 2. 定义目标数据库的连接信息
        String targetHostname = "your-tdsdata-host"; // 请替换为实际的 Kingbase 主机名或IP
        String targetDatabase = "tdsdata"; // 目标数据库名
        String targetUsername = "system";
        String targetPassword = "King@123";

        // 3. 为每个源数据库创建并执行同步作业
        // --- 3.1 源：cyxdms_retail ---
        createAndStartCDCJob(tableEnv,
                "cyxdms_retail",
                "cyxdms-retail-host", // 源数据库主机名
                "flink_user",
                "flink_password",
                new String[]{
                        "tt_invoice_upload",
                        "tt_invoice_upload_child",
                        "tt_orders_upload_log",
                        "tt_orders_upload_log_detail",
                        "tt_sales_order_detail",
                        "tt_sales_order_vin",
                        "tt_sales_orders"
                },
                targetHostname, targetDatabase, targetUsername, targetPassword);

        // --- 3.2 源：ep_sal ---
        createAndStartCDCJob(tableEnv,
                "ep_sal",
                "ep-sal-host", // 请替换为实际的数据库IP或主机名
                "flink_user",
                "flink_password", // 请替换为实际的密码
                new String[]{
                        "ck_dms_tt_orders_upload_log",
                        "ck_dms_tt_orders_upload_log_detail",
                        "mdac001d",
                        "mdac075",
                        "mdac100d1",
                        "mdac100d2",
                        "mdac100d3",
                        "mdac300",
                        "mdac301c",
                        "mdac302",
                        "mdai152",
                        "salb001",
                        "salb011",
                        "salb021",
                        "salb026",
                        "salb026_d",
                        "salb027",
                        "salb027_d",
                        "salb027_h",
                        "salb030c",
                        "salb030d",
                        "salb040c",
                        "salb040d",
                        "salb040d1",
                        "salb141c",
                        "salb141d",
                        "salc062",
                        "salc106c",
                        "salr021",
                        "salr052",
                        "sptb001cd",
                        "sptb021",
                        "sptb022",
                        "sptb022_d",
                        "sptb022_h",
                        "sptb050cd",
                        "spti006",
                        "sysc060d1",
                        "sysi051"
                },
                targetHostname, targetDatabase, targetUsername, targetPassword);

        // --- 3.3 源：ep_sc_sys ---
        createAndStartCDCJob(tableEnv,
                "ep_sc_sys",
                "ep-sc-sys-host",
                "flink_user",
                "flink_password",
                new String[]{"mdac100", "sysc005"},
                targetHostname, targetDatabase, targetUsername, targetPassword);

        // --- 3.4 源：ep_sc_vc ---
        createAndStartCDCJob(tableEnv,
                "ep_sc_vc",
                "ep-sc-vc-host",
                "flink_user",
                "flink_password",
                new String[]{"v_product"},
                targetHostname, targetDatabase, targetUsername, targetPassword);

        // 4. 启动所有任务
        // 注意：在 Table API/SQL 中，execute() 会触发所有已定义的作业。
        log.info("所有同步任务已定义，开始执行...");
        env.execute("Multi-Source CDC Sync Job to Kingbase");
    }

    /**
     * 为一个源数据库中的多个表创建CDC源表，并将数据插入到目标Kingbase数据库的同名表中。
     *
     * @param tableEnv Flink TableEnvironment
     * @param sourceDatabase 源数据库名
     * @param sourceHostname 源数据库主机名
     * @param sourceUsername 源数据库用户名
     * @param sourcePassword 源数据库密码
     * @param tableNames 源数据库中需要同步的表名数组
     * @param targetHostname 目标Kingbase数据库主机名
     * @param targetDatabase 目标Kingbase数据库名
     * @param targetUsername 目标数据库用户名
     * @param targetPassword 目标数据库密码
     */
    private static void createAndStartCDCJob(StreamTableEnvironment tableEnv,
                                             String sourceDatabase,
                                             String sourceHostname,
                                             String sourceUsername,
                                             String sourcePassword,
                                             String[] tableNames,
                                             String targetHostname,
                                             String targetDatabase,
                                             String targetUsername,
                                             String targetPassword) {

        for (String tableName : tableNames) {
            try {
                // 唯一的表标识符，用于Flink内部
                String uniqueTableName = sourceDatabase + "_" + tableName;

                // 1. 创建 MySQL CDC 源表 (Source Table)
                String createSourceSql = String.format(
                        "CREATE TABLE %s (" +
                                "   -- Flink CDC 会自动从数据库元数据推断所有字段，这里用 '...' 代表所有列" +
                                "   -- 实际上，Flink 会自动处理，无需手动列出所有字段" +
                                ") WITH (" +
                                "   'connector' = 'mysql-cdc'," +
                                "   'hostname' = '%s'," +
                                "   'port' = '3306'," +
                                "   'username' = '%s'," +
                                "   'password' = '%s'," +
                                "   'database-name' = '%s'," +
                                "   'table-name' = '%s'" +
                                ")",
                        uniqueTableName, // 目标表名
                        sourceHostname,
                        sourceUsername,
                        sourcePassword,
                        sourceDatabase,
                        tableName
                );
                tableEnv.executeSql(createSourceSql);
                log.info("成功创建源表: {}", uniqueTableName);

                // 2. 创建 Kingbase 目标表 (Sink Table)
                // 注意：目标表必须在Kingbase中预先创建好，且结构与源表完全相同。
                String createSinkSql = String.format(
                        "CREATE TABLE %s (" +
                                "   -- 同样，字段由Flink自动推断，或手动定义" +
                                ") WITH (" +
                                "   'connector' = 'jdbc'," +
                                "   'url' = '****************************'," +
                                "   'driver' = 'com.kingbase8.Driver'," +
                                "   'table-name' = '%s'," + // 关键：这里的 'table-name' 是目标数据库中实际的表名
                                "   'username' = '%s'," +
                                "   'password' = '%s'" +
                                ")",
                        uniqueTableName + "_sink", // Sink表名，与源表区分
                        targetHostname,
                        targetDatabase,
                        tableName, // ⚠️ 这里是核心：写入目标数据库中同名的表
                        targetUsername,
                        targetPassword
                );
                tableEnv.executeSql(createSinkSql);
                log.info("成功创建目标表: {}", uniqueTableName + "_sink");

                // 3. 执行数据插入 (INSERT INTO)
                // 将源表的数据插入到目标表
                String insertSql = String.format(
                        "INSERT INTO %s SELECT * FROM %s",
                        uniqueTableName + "_sink",
                        uniqueTableName
                );
                TableResult result = tableEnv.executeSql(insertSql);
                log.info("已启动数据同步任务: {} -> {}", uniqueTableName, uniqueTableName + "_sink");

            } catch (Exception e) {
                log.error("处理表 {}.{} 时发生错误: {}", sourceDatabase, tableName, e.getMessage(), e);
                // 可以选择继续处理其他表，或者抛出异常终止
                throw new RuntimeException(e);
            }
        }
    }
}