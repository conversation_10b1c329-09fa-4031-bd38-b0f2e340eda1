//import java.util.HashMap;
//import java.util.Map;
//
///**
// * 表结构配置类
// * 用于定义各个表的字段结构，支持MySQL和Kingbase数据库
// */
//public class TableSchemaConfig {
//
//    private static final Map<String, String> TABLE_SCHEMAS = new HashMap<>();
//
//    static {
//        // 初始化表结构定义
//        // 注意：这里需要根据实际的表结构进行配置
//
//        // cyxdms_retail 数据库表结构
//        TABLE_SCHEMAS.put("tt_invoice_upload",
//            "id BIGINT, " +
//            "invoice_no VARCHAR(50), " +
//            "upload_time TIMESTAMP(3), " +
//            "status INT, " +
//            "create_time TIMESTAMP(3), " +
//            "update_time TIMESTAMP(3), " +
//            "PRIMARY KEY (id) NOT ENFORCED");
//
//        TABLE_SCHEMAS.put("tt_invoice_upload_child",
//            "id BIGINT, " +
//            "parent_id BIGINT, " +
//            "item_code VARCHAR(50), " +
//            "quantity DECIMAL(10,2), " +
//            "price DECIMAL(10,2), " +
//            "create_time TIMESTAMP(3), " +
//            "PRIMARY KEY (id) NOT ENFORCED");
//
//        TABLE_SCHEMAS.put("tt_orders_upload_log",
//            "id BIGINT, " +
//            "order_no VARCHAR(50), " +
//            "upload_status INT, " +
//            "upload_time TIMESTAMP(3), " +
//            "error_msg VARCHAR(500), " +
//            "create_time TIMESTAMP(3), " +
//            "PRIMARY KEY (id) NOT ENFORCED");
//
//        TABLE_SCHEMAS.put("tt_orders_upload_log_detail",
//            "id BIGINT, " +
//            "log_id BIGINT, " +
//            "detail_info VARCHAR(1000), " +
//            "process_status INT, " +
//            "create_time TIMESTAMP(3), " +
//            "PRIMARY KEY (id) NOT ENFORCED");
//
//        TABLE_SCHEMAS.put("tt_sales_order_detail",
//            "id BIGINT, " +
//            "order_id BIGINT, " +
//            "product_code VARCHAR(50), " +
//            "product_name VARCHAR(200), " +
//            "quantity DECIMAL(10,2), " +
//            "unit_price DECIMAL(10,2), " +
//            "total_amount DECIMAL(10,2), " +
//            "create_time TIMESTAMP(3), " +
//            "PRIMARY KEY (id) NOT ENFORCED");
//
//        TABLE_SCHEMAS.put("tt_sales_order_vin",
//            "id BIGINT, " +
//            "order_id BIGINT, " +
//            "vin_code VARCHAR(50), " +
//            "status INT, " +
//            "create_time TIMESTAMP(3), " +
//            "PRIMARY KEY (id) NOT ENFORCED");
//
//        TABLE_SCHEMAS.put("tt_sales_orders",
//            "id BIGINT, " +
//            "order_no VARCHAR(50), " +
//            "customer_id BIGINT, " +
//            "order_date DATE, " +
//            "total_amount DECIMAL(12,2), " +
//            "status INT, " +
//            "create_time TIMESTAMP(3), " +
//            "update_time TIMESTAMP(3), " +
//            "PRIMARY KEY (id) NOT ENFORCED");
//
//        // ep_sal 数据库表结构（示例，需要根据实际情况调整）
//        TABLE_SCHEMAS.put("ck_dms_tt_orders_upload_log",
//            "id BIGINT, " +
//            "order_no VARCHAR(50), " +
//            "upload_status INT, " +
//            "upload_time TIMESTAMP(3), " +
//            "PRIMARY KEY (id) NOT ENFORCED");
//
//        TABLE_SCHEMAS.put("ck_dms_tt_orders_upload_log_detail",
//            "id BIGINT, " +
//            "log_id BIGINT, " +
//            "detail_info VARCHAR(1000), " +
//            "PRIMARY KEY (id) NOT ENFORCED");
//
//        // 其他表的通用结构（需要根据实际情况调整）
//        String[] epSalTables = {
//            "mdac001d", "mdac075", "mdac100d1", "mdac100d2", "mdac100d3", "mdac300",
//            "mdac301c", "mdac302", "mdai152", "salb001", "salb011", "salb021",
//            "salb026", "salb026_d", "salb027", "salb027_d", "salb027_h", "salb030c",
//            "salb030d", "salb040c", "salb040d", "salb040d1", "salb141c", "salb141d",
//            "salc062", "salc106c", "salr021", "salr052", "sptb001cd", "sptb021",
//            "sptb022", "sptb022_d", "sptb022_h", "sptb050cd", "spti006", "sysc060d1", "sysi051"
//        };
//
//        for (String tableName : epSalTables) {
//            if (!TABLE_SCHEMAS.containsKey(tableName)) {
//                TABLE_SCHEMAS.put(tableName,
//                    "id BIGINT, " +
//                    "code VARCHAR(50), " +
//                    "name VARCHAR(200), " +
//                    "status INT, " +
//                    "create_time TIMESTAMP(3), " +
//                    "update_time TIMESTAMP(3), " +
//                    "PRIMARY KEY (id) NOT ENFORCED");
//            }
//        }
//
//        // ep_sc_sys 数据库表结构
//        TABLE_SCHEMAS.put("mdac100",
//            "id BIGINT, " +
//            "code VARCHAR(50), " +
//            "name VARCHAR(200), " +
//            "type INT, " +
//            "status INT, " +
//            "create_time TIMESTAMP(3), " +
//            "PRIMARY KEY (id) NOT ENFORCED");
//
//        TABLE_SCHEMAS.put("sysc005",
//            "id BIGINT, " +
//            "system_code VARCHAR(50), " +
//            "system_name VARCHAR(200), " +
//            "config_value VARCHAR(500), " +
//            "create_time TIMESTAMP(3), " +
//            "PRIMARY KEY (id) NOT ENFORCED");
//
//        // ep_sc_vc 数据库表结构
//        TABLE_SCHEMAS.put("v_product",
//            "id BIGINT, " +
//            "product_code VARCHAR(50), " +
//            "product_name VARCHAR(200), " +
//            "category VARCHAR(100), " +
//            "price DECIMAL(10,2), " +
//            "status INT, " +
//            "create_time TIMESTAMP(3), " +
//            "PRIMARY KEY (id) NOT ENFORCED");
//    }
//
//    /**
//     * 获取表的字段定义
//     * @param tableName 表名
//     * @return 字段定义字符串
//     */
//    public static String getTableSchema(String tableName) {
//        return TABLE_SCHEMAS.getOrDefault(tableName,
//            "id BIGINT, " +
//            "data VARCHAR(1000), " +
//            "create_time TIMESTAMP(3), " +
//            "PRIMARY KEY (id) NOT ENFORCED");
//    }
//
//    /**
//     * 检查表是否有定义的结构
//     * @param tableName 表名
//     * @return 是否有定义
//     */
//    public static boolean hasTableSchema(String tableName) {
//        return TABLE_SCHEMAS.containsKey(tableName);
//    }
//}
