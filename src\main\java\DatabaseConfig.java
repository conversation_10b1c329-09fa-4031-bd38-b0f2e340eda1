/**
 * 数据库配置类
 * 用于管理源数据库和目标数据库的连接信息
 */
public class DatabaseConfig {
    
    /**
     * 数据库连接信息类
     */
    public static class DatabaseInfo {
        private final String hostname;
        private final int port;
        private final String database;
        private final String username;
        private final String password;
        // mysql, kingbase
        private final String databaseType;
        
        public DatabaseInfo(String hostname, int port, String database, 
                          String username, String password, String databaseType) {
            this.hostname = hostname;
            this.port = port;
            this.database = database;
            this.username = username;
            this.password = password;
            this.databaseType = databaseType;
        }
        
        // Getters
        public String getHostname() { return hostname; }
        public int getPort() { return port; }
        public String getDatabase() { return database; }
        public String getUsername() { return username; }
        public String getPassword() { return password; }
        public String getDatabaseType() { return databaseType; }
        
        /**
         * 获取JDBC URL
         */
        public String getJdbcUrl() {
            switch (databaseType.toLowerCase()) {
                case "mysql":
                    return String.format("*****************************************************", 
                                       hostname, port, database);
                case "kingbase":
                    return String.format("jdbc:kingbase8://%s:%d/%s", hostname, port, database);
                default:
                    throw new IllegalArgumentException("Unsupported database type: " + databaseType);
            }
        }
        
        /**
         * 获取JDBC驱动类名
         */
        public String getDriverClass() {
            switch (databaseType.toLowerCase()) {
                case "mysql":
                    return "com.mysql.cj.jdbc.Driver";
                case "kingbase":
                    return "com.kingbase8.Driver";
                default:
                    throw new IllegalArgumentException("Unsupported database type: " + databaseType);
            }
        }
    }
    
    // 目标数据库配置
    public static final DatabaseInfo TARGET_DATABASE = new DatabaseInfo(
        System.getProperty("target.hostname", "your-tdsdata-host"),
        Integer.parseInt(System.getProperty("target.port", "54321")),
        System.getProperty("target.database", "tdsdata"),
        System.getProperty("target.username", "system"),
        System.getProperty("target.password", "King@123"),
        "kingbase"
    );
    
    // 源数据库配置
    public static final DatabaseInfo CYXDMS_RETAIL = new DatabaseInfo(
        System.getProperty("cyxdms.hostname", "cyxdms-retail-host"),
        Integer.parseInt(System.getProperty("cyxdms.port", "3306")),
        "cyxdms_retail",
        System.getProperty("cyxdms.username", "flink_user"),
        System.getProperty("cyxdms.password", "flink_password"),
        "mysql"
    );
    
    public static final DatabaseInfo EP_SAL = new DatabaseInfo(
        System.getProperty("epsal.hostname", "ep-sal-host"),
        Integer.parseInt(System.getProperty("epsal.port", "3306")),
        "ep_sal",
        System.getProperty("epsal.username", "flink_user"),
        System.getProperty("epsal.password", "flink_password"),
        "mysql"
    );
    
    public static final DatabaseInfo EP_SC_SYS = new DatabaseInfo(
        System.getProperty("epscsys.hostname", "ep-sc-sys-host"),
        Integer.parseInt(System.getProperty("epscsys.port", "3306")),
        "ep_sc_sys",
        System.getProperty("epscsys.username", "flink_user"),
        System.getProperty("epscsys.password", "flink_password"),
        "mysql"
    );
    
    public static final DatabaseInfo EP_SC_VC = new DatabaseInfo(
        System.getProperty("epscvc.hostname", "ep-sc-vc-host"),
        Integer.parseInt(System.getProperty("epscvc.port", "3306")),
        "ep_sc_vc",
        System.getProperty("epscvc.username", "flink_user"),
        System.getProperty("epscvc.password", "flink_password"),
        "mysql"
    );
    
    /**
     * 表配置信息
     */
    public static class TableConfig {
        private final DatabaseInfo sourceDatabase;
        private final String[] tableNames;
        
        public TableConfig(DatabaseInfo sourceDatabase, String[] tableNames) {
            this.sourceDatabase = sourceDatabase;
            this.tableNames = tableNames;
        }
        
        public DatabaseInfo getSourceDatabase() { return sourceDatabase; }
        public String[] getTableNames() { return tableNames; }
    }
    
    // 所有需要同步的表配置
    public static final TableConfig[] TABLE_CONFIGS = {
        new TableConfig(CYXDMS_RETAIL, new String[]{
            "tt_invoice_upload",
            "tt_invoice_upload_child",
            "tt_orders_upload_log",
            "tt_orders_upload_log_detail",
            "tt_sales_order_detail",
            "tt_sales_order_vin",
            "tt_sales_orders"
        }),
        
        new TableConfig(EP_SAL, new String[]{
            "ck_dms_tt_orders_upload_log",
            "ck_dms_tt_orders_upload_log_detail",
            "mdac001d", "mdac075", "mdac100d1", "mdac100d2", "mdac100d3",
            "mdac300", "mdac301c", "mdac302", "mdai152",
            "salb001", "salb011", "salb021", "salb026", "salb026_d",
            "salb027", "salb027_d", "salb027_h", "salb030c", "salb030d",
            "salb040c", "salb040d", "salb040d1", "salb141c", "salb141d",
            "salc062", "salc106c", "salr021", "salr052",
            "sptb001cd", "sptb021", "sptb022", "sptb022_d", "sptb022_h",
            "sptb050cd", "spti006", "sysc060d1", "sysi051"
        }),
        
        new TableConfig(EP_SC_SYS, new String[]{
            "mdac100", "sysc005"
        }),
        
        new TableConfig(EP_SC_VC, new String[]{
            "v_product"
        })
    };
}
