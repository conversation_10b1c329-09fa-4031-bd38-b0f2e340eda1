#!/bin/bash

set -e

# 设置默认的环境变量
export JAVA_OPTS="${JAVA_OPTS:--Xmx2g -Xms1g}"

# 数据库连接配置
export TARGET_HOSTNAME="${TARGET_HOSTNAME:-your-tdsdata-host}"
export TARGET_PORT="${TARGET_PORT:-54321}"
export TARGET_DATABASE="${TARGET_DATABASE:-tdsdata}"
export TARGET_USERNAME="${TARGET_USERNAME:-system}"
export TARGET_PASSWORD="${TARGET_PASSWORD:-King@123}"

export CYXDMS_HOSTNAME="${CYXDMS_HOSTNAME:-cyxdms-retail-host}"
export CYXDMS_PORT="${CYXDMS_PORT:-3306}"
export CYXDMS_USERNAME="${CYXDMS_USERNAME:-flink_user}"
export CYXDMS_PASSWORD="${CYXDMS_PASSWORD:-flink_password}"

export EPSAL_HOSTNAME="${EPSAL_HOSTNAME:-ep-sal-host}"
export EPSAL_PORT="${EPSAL_PORT:-3306}"
export EPSAL_USERNAME="${EPSAL_USERNAME:-flink_user}"
export EPSAL_PASSWORD="${EPSAL_PASSWORD:-flink_password}"

export EPSCSYS_HOSTNAME="${EPSCSYS_HOSTNAME:-ep-sc-sys-host}"
export EPSCSYS_PORT="${EPSCSYS_PORT:-3306}"
export EPSCSYS_USERNAME="${EPSCSYS_USERNAME:-flink_user}"
export EPSCSYS_PASSWORD="${EPSCSYS_PASSWORD:-flink_password}"

export EPSCVC_HOSTNAME="${EPSCVC_HOSTNAME:-ep-sc-vc-host}"
export EPSCVC_PORT="${EPSCVC_PORT:-3306}"
export EPSCVC_USERNAME="${EPSCVC_USERNAME:-flink_user}"
export EPSCVC_PASSWORD="${EPSCVC_PASSWORD:-flink_password}"

# 函数：等待数据库连接
wait_for_database() {
    local host=$1
    local port=$2
    local timeout=${3:-30}
    
    echo "等待数据库 $host:$port 连接..."
    for i in $(seq 1 $timeout); do
        if nc -z $host $port; then
            echo "数据库 $host:$port 连接成功"
            return 0
        fi
        echo "等待数据库连接... ($i/$timeout)"
        sleep 1
    done
    echo "数据库 $host:$port 连接超时"
    return 1
}

# 检查数据库连接
check_databases() {
    echo "检查数据库连接..."
    
    # 检查目标数据库
    wait_for_database $TARGET_HOSTNAME $TARGET_PORT
    
    # 检查源数据库（可选，因为可能在不同的网络中）
    # wait_for_database $CYXDMS_HOSTNAME $CYXDMS_PORT
    # wait_for_database $EPSAL_HOSTNAME $EPSAL_PORT
    # wait_for_database $EPSCSYS_HOSTNAME $EPSCSYS_PORT
    # wait_for_database $EPSCVC_HOSTNAME $EPSCVC_PORT
}

# 启动应用程序
start_app() {
    echo "启动 Flink CDC 应用程序..."
    
    # 设置 JVM 参数
    JAVA_OPTS="$JAVA_OPTS \
        -Dtarget.hostname=$TARGET_HOSTNAME \
        -Dtarget.port=$TARGET_PORT \
        -Dtarget.database=$TARGET_DATABASE \
        -Dtarget.username=$TARGET_USERNAME \
        -Dtarget.password=$TARGET_PASSWORD \
        -Dcyxdms.hostname=$CYXDMS_HOSTNAME \
        -Dcyxdms.port=$CYXDMS_PORT \
        -Dcyxdms.username=$CYXDMS_USERNAME \
        -Dcyxdms.password=$CYXDMS_PASSWORD \
        -Depsal.hostname=$EPSAL_HOSTNAME \
        -Depsal.port=$EPSAL_PORT \
        -Depsal.username=$EPSAL_USERNAME \
        -Depsal.password=$EPSAL_PASSWORD \
        -Depscsys.hostname=$EPSCSYS_HOSTNAME \
        -Depscsys.port=$EPSCSYS_PORT \
        -Depscsys.username=$EPSCSYS_USERNAME \
        -Depscsys.password=$EPSCSYS_PASSWORD \
        -Depscvc.hostname=$EPSCVC_HOSTNAME \
        -Depscvc.port=$EPSCVC_PORT \
        -Depscvc.username=$EPSCVC_USERNAME \
        -Depscvc.password=$EPSCVC_PASSWORD"
    
    # 启动应用程序
    exec java $JAVA_OPTS -jar /opt/flink-cdc-app/app.jar
}

# 主逻辑
case "$1" in
    run)
        check_databases
        start_app
        ;;
    bash)
        exec /bin/bash
        ;;
    *)
        echo "用法: $0 {run|bash}"
        echo "  run  - 启动 Flink CDC 应用程序"
        echo "  bash - 启动 bash shell"
        exit 1
        ;;
esac
