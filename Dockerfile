# 使用官方的 OpenJDK 17 作为基础镜像
FROM openjdk:17-jdk-slim

# 设置工作目录
WORKDIR /opt/flink-cdc-app

# 设置环境变量
ENV JAVA_OPTS="-Xmx2g -Xms1g"
ENV FLINK_HOME=/opt/flink
ENV PATH=$FLINK_HOME/bin:$PATH

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 下载并安装 Flink 1.13.5
RUN wget -q https://archive.apache.org/dist/flink/flink-1.13.5/flink-1.13.5-bin-scala_2.12.tgz \
    && tar -xzf flink-1.13.5-bin-scala_2.12.tgz \
    && mv flink-1.13.5 $FLINK_HOME \
    && rm flink-1.13.5-bin-scala_2.12.tgz

# 复制应用程序 JAR 文件
COPY target/flink-cdc-multi-source-sync-1.0-SNAPSHOT.jar /opt/flink-cdc-app/app.jar

# 复制本地依赖（如果有）
COPY lib/ /opt/flink-cdc-app/lib/

# 创建日志目录
RUN mkdir -p /opt/flink-cdc-app/logs

# 设置启动脚本
COPY docker-entrypoint.sh /opt/flink-cdc-app/
RUN chmod +x /opt/flink-cdc-app/docker-entrypoint.sh

# 暴露端口（如果需要）
EXPOSE 8081 6123

# 设置启动命令
ENTRYPOINT ["/opt/flink-cdc-app/docker-entrypoint.sh"]
CMD ["run"]
