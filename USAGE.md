# 使用指南

## 简化架构说明

本项目已经简化为基于 DataStream API 的实现，相比原来的 Table API 方案有以下优势：

### 1. 无需定义表结构
- **原来**: 需要在 `TableSchemaConfig` 中手动定义每个表的字段
- **现在**: CDC 自动捕获表结构，无需手动配置

### 2. 直接数据流处理
- **原来**: 使用复杂的 SQL DDL 创建源表和目标表
- **现在**: 直接处理 CDC 事件流，自动解析 JSON 数据

### 3. 更简单的依赖
- **原来**: 需要多个 Table API 相关依赖
- **现在**: 只需要核心的 DataStream API 和 CDC 连接器

## 核心类说明

### SimplifiedCDCJob.java
主要的作业类，负责：
- 创建 CDC 数据源
- 处理 CDC 事件（INSERT、UPDATE、DELETE）
- 调用数据库操作工具执行同步

### DatabaseOperator.java
数据库操作工具类，负责：
- 管理目标数据库连接
- 执行 INSERT、UPDATE、DELETE 操作
- 处理数据库异常和重试

### DatabaseConfig.java
配置管理类，负责：
- 定义源数据库和目标数据库连接信息
- 配置需要同步的表列表
- 支持环境变量配置

## 数据流处理流程

```
源数据库变更 → CDC捕获 → JSON事件 → 解析处理 → 目标数据库操作
     ↓              ↓         ↓         ↓           ↓
   MySQL/         Debezium   事件类型   字段映射    INSERT/
   Kingbase       格式       识别       (1:1)      UPDATE/
                                                  DELETE
```

## 配置步骤

### 1. 数据库连接配置

在 `DatabaseConfig.java` 中配置数据库连接：

```java
// 目标数据库
public static final DatabaseInfo TARGET_DATABASE = new DatabaseInfo(
    "your-kingbase-host",  // 主机名
    54321,                 // 端口
    "tdsdata",            // 数据库名
    "system",             // 用户名
    "your-password",      // 密码
    "kingbase"            // 数据库类型
);

// 源数据库
public static final DatabaseInfo SOURCE_DB = new DatabaseInfo(
    "your-mysql-host",
    3306,
    "source_db",
    "username",
    "password",
    "mysql"
);
```

### 2. 表同步配置

配置需要同步的表：

```java
public static final TableConfig[] TABLE_CONFIGS = {
    new TableConfig(SOURCE_DB, new String[]{
        "table1",
        "table2", 
        "table3"
    })
};
```

### 3. 环境变量配置

可以通过环境变量覆盖默认配置：

```bash
# 目标数据库
export TARGET_HOSTNAME=your-kingbase-host
export TARGET_PASSWORD=your-password

# 源数据库
export SOURCE_HOSTNAME=your-mysql-host
export SOURCE_PASSWORD=your-password
```

## 运行方式

### 本地运行

```bash
# 编译项目
mvn clean compile

# 运行作业
mvn exec:java -Dexec.mainClass="SimplifiedCDCJob"
```

### Docker 运行

```bash
# 构建镜像
docker build -t flink-cdc-sync .

# 运行容器
docker run -d \
  -e TARGET_HOSTNAME=your-kingbase-host \
  -e TARGET_PASSWORD=your-password \
  -e SOURCE_HOSTNAME=your-mysql-host \
  -e SOURCE_PASSWORD=your-password \
  flink-cdc-sync
```

### Kubernetes 部署

```bash
# 部署到 K8s
kubectl apply -f k8s-deployment.yaml

# 查看状态
kubectl get pods -l app=flink-cdc-multi-source-sync

# 查看日志
kubectl logs -l app=flink-cdc-multi-source-sync -f
```

## 监控和调试

### 查看 CDC 事件

CDC 事件的 JSON 格式示例：

```json
{
  "op": "c",  // 操作类型: c=create, u=update, d=delete, r=read
  "source": {
    "db": "source_database",
    "table": "table_name"
  },
  "before": null,  // 变更前数据 (UPDATE/DELETE时有值)
  "after": {       // 变更后数据 (INSERT/UPDATE时有值)
    "id": 1,
    "name": "example",
    "create_time": "2023-01-01T00:00:00Z"
  }
}
```

### 常见问题排查

1. **连接失败**
   - 检查网络连通性
   - 验证数据库凭据
   - 确认防火墙设置

2. **表不存在**
   - 确保目标数据库中存在同名表
   - 检查表结构是否一致

3. **权限不足**
   - 确保源数据库用户有 REPLICATION 权限
   - 确保目标数据库用户有 INSERT/UPDATE/DELETE 权限

## 性能优化

### 1. 并行度调整
```java
env.setParallelism(4); // 根据CPU核心数调整
```

### 2. 检查点配置
```java
env.enableCheckpointing(30_000); // 30秒检查点间隔
```

### 3. 批量写入优化
在 `DatabaseOperator` 中可以实现批量操作来提高性能。

## 扩展功能

### 1. 错误处理和重试
可以在 `CDCEventProcessor` 中添加重试逻辑。

### 2. 数据转换
如果需要数据转换，可以在处理 CDC 事件时添加转换逻辑。

### 3. 监控指标
可以集成 Flink 的 Metrics 系统来监控同步状态。
