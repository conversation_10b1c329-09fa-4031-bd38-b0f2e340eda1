#!/bin/bash

# Flink CDC 多源同步项目构建和部署脚本

set -e

# 配置变量
PROJECT_NAME="flink-cdc-multi-source-sync"
VERSION="1.0-SNAPSHOT"
DOCKER_REGISTRY="your-registry"  # 请替换为实际的 Docker 镜像仓库地址
NAMESPACE="default"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_prerequisites() {
    log_info "检查必要的工具..."
    
    if ! command -v mvn &> /dev/null; then
        log_error "Maven 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装或不在 PATH 中"
        exit 1
    fi
    
    log_info "所有必要工具检查完成"
}

# 构建 Maven 项目
build_maven() {
    log_info "开始构建 Maven 项目..."
    
    # 清理并编译
    mvn clean compile
    
    # 运行测试（如果有）
    # mvn test
    
    # 打包
    mvn package -DskipTests
    
    if [ ! -f "target/${PROJECT_NAME}-${VERSION}.jar" ]; then
        log_error "Maven 构建失败，JAR 文件不存在"
        exit 1
    fi
    
    log_info "Maven 构建完成"
}

# 构建 Docker 镜像
build_docker() {
    log_info "开始构建 Docker 镜像..."
    
    local image_tag="${DOCKER_REGISTRY}/${PROJECT_NAME}:${VERSION}"
    local latest_tag="${DOCKER_REGISTRY}/${PROJECT_NAME}:latest"
    
    # 构建镜像
    docker build -t $image_tag -t $latest_tag .
    
    log_info "Docker 镜像构建完成: $image_tag"
}

# 推送 Docker 镜像
push_docker() {
    log_info "推送 Docker 镜像到仓库..."
    
    local image_tag="${DOCKER_REGISTRY}/${PROJECT_NAME}:${VERSION}"
    local latest_tag="${DOCKER_REGISTRY}/${PROJECT_NAME}:latest"
    
    # 推送镜像
    docker push $image_tag
    docker push $latest_tag
    
    log_info "Docker 镜像推送完成"
}

# 部署到 Kubernetes
deploy_k8s() {
    log_info "部署到 Kubernetes..."
    
    # 更新部署文件中的镜像标签
    local image_tag="${DOCKER_REGISTRY}/${PROJECT_NAME}:${VERSION}"
    sed -i.bak "s|your-registry/flink-cdc-multi-source-sync:latest|${image_tag}|g" k8s-deployment.yaml
    
    # 应用配置
    kubectl apply -f k8s-deployment.yaml -n $NAMESPACE
    
    # 等待部署完成
    log_info "等待部署完成..."
    kubectl rollout status deployment/${PROJECT_NAME} -n $NAMESPACE --timeout=300s
    
    # 恢复原始文件
    mv k8s-deployment.yaml.bak k8s-deployment.yaml
    
    log_info "Kubernetes 部署完成"
}

# 检查部署状态
check_deployment() {
    log_info "检查部署状态..."
    
    # 检查 Pod 状态
    kubectl get pods -l app=${PROJECT_NAME} -n $NAMESPACE
    
    # 检查服务状态
    kubectl get svc -l app=${PROJECT_NAME} -n $NAMESPACE
    
    # 显示日志
    log_info "最近的应用日志:"
    kubectl logs -l app=${PROJECT_NAME} -n $NAMESPACE --tail=20
}

# 清理资源
cleanup() {
    log_info "清理 Kubernetes 资源..."
    kubectl delete -f k8s-deployment.yaml -n $NAMESPACE --ignore-not-found=true
    log_info "清理完成"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build     - 构建 Maven 项目和 Docker 镜像"
    echo "  push      - 推送 Docker 镜像到仓库"
    echo "  deploy    - 部署到 Kubernetes"
    echo "  status    - 检查部署状态"
    echo "  cleanup   - 清理 Kubernetes 资源"
    echo "  all       - 执行完整的构建和部署流程"
    echo "  help      - 显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  DOCKER_REGISTRY - Docker 镜像仓库地址 (默认: your-registry)"
    echo "  NAMESPACE       - Kubernetes 命名空间 (默认: default)"
}

# 主函数
main() {
    case "${1:-help}" in
        build)
            check_prerequisites
            build_maven
            build_docker
            ;;
        push)
            check_prerequisites
            push_docker
            ;;
        deploy)
            check_prerequisites
            deploy_k8s
            check_deployment
            ;;
        status)
            check_deployment
            ;;
        cleanup)
            cleanup
            ;;
        all)
            check_prerequisites
            build_maven
            build_docker
            push_docker
            deploy_k8s
            check_deployment
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
