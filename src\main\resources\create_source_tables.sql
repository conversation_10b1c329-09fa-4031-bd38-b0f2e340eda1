-- 创建 cyxdms_retail 的所有需要同步的表
CREATE TABLE cyxdms_retail_source (
  -- 注意：这里不定义具体字段，让CDC自动发现
  -- 但为了SQL语法正确，可以定义一个ANY类型或使用其他技巧
  -- 实际上，我们更推荐为每个表单独建表，但用脚本生成
) WITH (
  'connector' = 'mysql-cdc',
  'hostname' = 'your-cyxdms-retail-host',
  'port' = '3306',
  'username' = 'flink_user',
  'password' = 'flink_password',
  'database-name' = 'cyxdms_retail',
  'table-name' = 'tt_invoice_upload|tt_invoice_upload_child|tt_orders_upload_log|tt_orders_upload_log_detail|tt_sales_order_detail|tt_sales_order_vin|tt_sales_orders' -- 正则表达式
);